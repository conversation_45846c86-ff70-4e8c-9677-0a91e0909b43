version: '3.8'
services:
    mysql:
        image: mysql:8.0
        restart: always
        environment:
            MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASS}
            MYSQL_DATABASE: ${DB_NAME}
            MYSQL_USER: ${DB_USER}
            MYSQL_PASSWORD: ${DB_PASS}
        ports:
            - "3306:3306"
        volumes:
            - mysql_data:/var/lib/mysql

    bot:
        build: .
        restart: always
        stop_signal: SIGINT
        depends_on:
            - mysql
        ports:
            - "${WEBHOOK_PORT}:${WEBHOOK_PORT}"
        environment:
            BOT_TOKEN: ${BOT_TOKEN}
            SHOP_NAME: ${SHOP_NAME}
            PROTOCOLS: ${PROTOCOLS}
            TEST_PERIOD: ${TEST_PERIOD}
            PERIOD_LIMIT: ${PERIOD_LIMIT}
            ABOUT: ${ABOUT}
            RULES_LINK: ${RULES_LINK}
            SUPPORT_LINK: ${SUPPORT_LINK}
            YOOKASSA_TOKEN: ${YOOKASSA_TOKEN}
            YOOKASSA_SHOPID: ${YOOKASSA_SHOPID}
            EMAIL: ${EMAIL}
            CRYPTO_TOKEN: ${CRYPTO_TOKEN}
            MERCHANT_UUID: ${MERCHANT_UUID}
            DB_NAME: ${DB_NAME}
            DB_USER: ${DB_USER}
            DB_PASS: ${DB_PASS}
            DB_ADDRESS: ${DB_ADDRESS}
            PANEL_HOST: ${PANEL_HOST}
            PANEL_GLOBAL: ${PANEL_GLOBAL}
            PANEL_USER: ${PANEL_USER}
            PANEL_PASS: ${PANEL_PASS}
            WEBHOOK_URL: ${WEBHOOK_URL}
            WEBHOOK_PORT: ${WEBHOOK_PORT}
            RENEW_NOTIFICATION_TIME: ${RENEW_NOTIFICATION_TIME}
        volumes:
            - "./goods.json:/app/goods.json"
            - "./locales:/app/locales"

volumes:
    mysql_data: